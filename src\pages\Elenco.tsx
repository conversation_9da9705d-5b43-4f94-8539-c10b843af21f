import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { MoreHorizontal, Plus, Search, Filter } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/ui/data-table";
import { Input } from "@/components/ui/input";
import { PlayerDialog } from "@/components/modals/PlayerDialog";
import { PlayerCard } from "@/components/player/PlayerCard";
import { PendingEvaluationsTable } from "@/components/evaluation/PendingEvaluationsTable";
import { EvaluationApprovalStats } from "@/components/evaluation/EvaluationApprovalStats";
import { usePlayersStore } from "@/store/usePlayersStore";
import { useClubMembersStore } from "@/store/useClubMembersStore";
import { useCategoriesStore } from "@/store/useCategoriesStore";
import type { Player, ClubMember } from "@/api/api";
import { initializePlayerStats, syncPlayerAggregatedStats } from "@/api/api";
import { useCurrentClubId } from "@/context/ClubContext";
import { checkAndUpdateLoanedPlayers } from "@/utils/loanManager";
import { useToast } from "@/components/ui/use-toast";
import { usePermission } from "@/hooks/usePermission";

export default function Elenco() {
  const [activeTab, setActiveTab] = useState("lista");
  const navigate = useNavigate();
  const [playerDialogOpen, setPlayerDialogOpen] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const [editMode, setEditMode] = useState(false);
  const { toast } = useToast();
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const { role } = usePermission();

  const canApproveEvaluations = role === "manager" || role === "president" || role === "admin";

  // Zustand store
  const { players, loading, error, fetchPlayers, updatePlayer, deletePlayer } = usePlayersStore();
  const { members, loading: loadingMembers, error: errorMembers, fetchMembers, addMember, removeMember } = useClubMembersStore();
  const { getCategoryPlayers } = useCategoriesStore();

  const clubId = useCurrentClubId();
  const [categoryPlayers, setCategoryPlayers] = useState<Player[]>([]);
  const [loadingCategoryPlayers, setLoadingCategoryPlayers] = useState(false);

  useEffect(() => {
    fetchPlayers(clubId);
    fetchMembers(clubId);

    // Verificar jogadores emprestados cujo empréstimo terminou
    checkAndUpdateLoanedPlayers(clubId).then(count => {
      if (count > 0) {
        toast({
          title: "Jogadores retornaram de empréstimo",
          description: `${count} jogador(es) retornaram automaticamente de empréstimo.`,
          variant: "default",
        });

        // Recarregar a lista de jogadores para refletir as mudanças
        fetchPlayers(clubId);
      }
    });
  }, [fetchPlayers, fetchMembers, clubId, toast]);

  // Listen for category changes from localStorage and custom events
  useEffect(() => {
    const storedCategoryId = localStorage.getItem("selectedCategoryId");
    if (storedCategoryId) {
      setSelectedCategoryId(Number(storedCategoryId));
    } else {
      setSelectedCategoryId(null);
    }

    // Add event listener for storage changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "selectedCategoryId") {
        if (e.newValue) {
          setSelectedCategoryId(Number(e.newValue));
        } else {
          setSelectedCategoryId(null);
        }
      }
    };

    // Escutar o evento personalizado de mudança de categoria
    const handleCategoryChanged = (e: CustomEvent) => {
      const { categoryId } = e.detail;
      setSelectedCategoryId(categoryId);
    };

    // Criar um observador para monitorar mudanças no localStorage em tempo real
    const checkForChanges = setInterval(() => {
      const currentCategoryId = localStorage.getItem("selectedCategoryId");
      const currentSelectedId = selectedCategoryId ? selectedCategoryId.toString() : null;

      if (currentCategoryId !== currentSelectedId) {
        if (currentCategoryId) {
          setSelectedCategoryId(Number(currentCategoryId));
        } else {
          setSelectedCategoryId(null);
        }
      }
    }, 500);

    window.addEventListener("storage", handleStorageChange);
    window.addEventListener("categoryChanged", handleCategoryChanged as EventListener);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener("categoryChanged", handleCategoryChanged as EventListener);
      clearInterval(checkForChanges);
    };
  }, [selectedCategoryId]);

  // Fetch players by category when selectedCategoryId changes
  useEffect(() => {
    if (selectedCategoryId && clubId) {
      setLoadingCategoryPlayers(true);
      getCategoryPlayers(clubId, selectedCategoryId)
        .then(players => {
          setCategoryPlayers(players);
        })
        .catch(error => {
          console.error("Error fetching category players:", error);
          setCategoryPlayers([]);
        })
        .finally(() => {
          setLoadingCategoryPlayers(false);
        });
    } else {
      setCategoryPlayers([]);
    }
  }, [selectedCategoryId, clubId, getCategoryPlayers]);

  // Função para navegar para o perfil do jogador
  const handleViewProfile = (playerId: string) => {
    navigate(`/jogador/${playerId}`);
  };

  // Colunas para a tabela de jogadores
  const statusOptions = [
    { label: "Disponível", value: "disponivel" },
    { label: "Lesionado", value: "lesionado" },
    { label: "Suspenso", value: "suspenso" },
    { label: "Em recuperação", value: "em recuperacao" },
    { label: "Inativo", value: "inativo" },
    { label: "Emprestado", value: "emprestado" },
    { label: "Em Avaliação", value: "em avaliacao" },
  ];

  const playerColumns = [
    {
      key: "name",
      header: "Jogador",
      searchable: true,
      render: (player: Player) => (
        <div className="flex items-center gap-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={player.image || ""} />
            <AvatarFallback className="bg-team-blue text-white">
              {player.name.slice(0, 2).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <Button
            variant="link"
            className="p-0 h-auto text-left font-medium"
            onClick={(e) => {
              e.stopPropagation();
              handleViewProfile(player.id);
            }}
          >
            {player.name}
            <span className="ml-2 text-xs bg-gray-100 px-1 rounded">
              #{player.number}
            </span>
          </Button>
        </div>
      ),
    },
    { key: "position", header: "Posição", searchable: true },
    { key: "age", header: "Idade" },
    { key: "height", header: "Altura" },
    { key: "weight", header: "Peso" },
    { key: "nationality", header: "Nacionalidade", searchable: true },
    {
      key: "status",
      header: "Status",
      render: (player: Player) => (
        <select
          value={player.status || "disponivel"}
          onChange={async (e) => {
            const newStatus = e.target.value;

            // Se o status for alterado para 'inativo', mostrar confirmação
            if (newStatus === 'inativo') {
              const confirmed = window.confirm(
                `Tem certeza que deseja alterar o status de ${player.name} para "Inativo"?\n\n` +
                'Esta ação irá remover o jogador de:\n' +
                '• Todas as categorias\n' +
                '• Alojamentos ativos\n' +
                '• Salários ativos\n' +
                '• Treinamentos futuros\n' +
                '• Convocações futuras\n' +
                '• Agenda de eventos\n' +
                '• Registros médicos pendentes\n\n' +
                'O histórico será preservado para consulta.'
              );

              if (!confirmed) {
                // Reverter a seleção se o usuário cancelar
                e.target.value = player.status || "disponivel";
                return;
              }
            }

            try {
              await updatePlayer(player.club_id, player.id, { status: newStatus });

              if (newStatus === 'inativo') {
                alert(`${player.name} foi alterado para "Inativo" e removido de todas as vinculações ativas.`);
              }

              fetchPlayers(player.club_id); // Atualiza a lista
            } catch (error) {
              console.error('Erro ao atualizar status:', error);
              alert('Erro ao atualizar o status do jogador. Tente novamente.');
              // Reverter a seleção em caso de erro
              e.target.value = player.status || "disponivel";
            }
          }}
          className={`border rounded px-2 py-1 text-xs relative z-50
            ${player.status === "disponivel" ? "bg-green-50 text-green-800 border-green-200" :
              player.status === "lesionado" ? "bg-rose-50 text-rose-800 border-rose-200" :
              player.status === "suspenso" ? "bg-amber-50 text-amber-800 border-amber-200" :
              player.status === "em recuperacao" ? "bg-orange-50 text-orange-800 border-orange-200" :
              player.status === "inativo" ? "bg-gray-50 text-gray-800 border-gray-200" :
              player.status === "emprestado" ? "bg-blue-50 text-blue-800 border-blue-200" : ""}
          `}
          style={{ position: "relative", zIndex: 50 }}
          onClick={e => e.stopPropagation()} // Evita propagação do clique
        >
          {statusOptions.map(opt => (
            <option key={opt.value} value={opt.value}>{opt.label}</option>
          ))}
        </select>
      ),
    },
  ];

  // Filtros para jogadores
  const playerFilters = [
    {
      key: "position",
      label: "Posição",
      options: [
        { label: "Goleiro", value: "Goleiro" },
        { label: "Zagueiro", value: "Zagueiro" },
        { label: "Lateral", value: "Lateral" },
        { label: "Meio-campo", value: "Meio-campo" },
        { label: "Atacante", value: "Atacante" },
      ],
    },
    {
      key: "status",
      label: "Status",
      options: [
        { label: "Disponível", value: "disponivel" },
        { label: "Lesionado", value: "lesionado" },
        { label: "Suspenso", value: "suspenso" },
        { label: "Em recuperação", value: "em recuperacao" },
        { label: "Inativo", value: "inativo" },
        { label: "Emprestado", value: "emprestado" },
      ],
    },
  ];

  // Ações para jogadores
  const playerActions = (player: Player) => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleViewProfile(player.id)}>Ver perfil</DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleEditPlayer(player)}>Editar</DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => navigate(`/jogador/${player.id}/historico-medico`)}>Histórico médico</DropdownMenuItem>
        <DropdownMenuItem onClick={() => navigate(`/jogador/${player.id}/estatisticas`)}>Estatísticas</DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem className="text-red-600">
          Remover do elenco
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );



  // Colunas para a tabela de membros do clube
  const memberColumns = [
    {
      key: "userName",
      header: "Nome",
      render: (member: ClubMember) => (
        <div className="flex items-center gap-3">
          <Avatar className="h-8 w-8">
            <AvatarFallback className="bg-team-blue text-white">
              {member.userName ? member.userName.slice(0, 2).toUpperCase() : 'UN'}
            </AvatarFallback>
          </Avatar>
          <span>{member.userName || 'Usuário sem nome'}</span>
        </div>
      ),
    },
    { key: "userEmail", header: "Email" },
    { key: "role", header: "Cargo" },
    { key: "status", header: "Status" },
  ];

  // Ações para membros do clube
  const memberActions = (member: ClubMember) => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => removeMember(member.userId, member.clubId)} className="text-red-600">
          Remover do clube
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  const handleEditPlayer = async (player: Player) => {
    const freshPlayer = players.find(p => p.id === player.id) || player;
    setSelectedPlayer(freshPlayer);
    setEditMode(true);
    setPlayerDialogOpen(true);
  };

  const handleInitializePlayerStats = async () => {
    try {
      await initializePlayerStats(clubId);
      toast({
        title: "Estatísticas inicializadas",
        description: "Estatísticas padrão foram criadas para jogadores sem estatísticas.",
      });
    } catch (error) {
      console.error("Erro ao inicializar estatísticas:", error);
      toast({
        title: "Erro",
        description: "Não foi possível inicializar as estatísticas dos jogadores.",
        variant: "destructive",
      });
    }
  };

  const handleSyncPlayerStats = async () => {
    try {
      await syncPlayerAggregatedStats(clubId);
      toast({
        title: "Estatísticas sincronizadas",
        description: "Todas as estatísticas dos jogadores foram recalculadas e atualizadas.",
      });
    } catch (error) {
      console.error("Erro ao sincronizar estatísticas:", error);
      toast({
        title: "Erro",
        description: "Não foi possível sincronizar as estatísticas dos jogadores.",
        variant: "destructive",
      });
    }
  };

  // Estado para filtro de busca de jogadores nos cards
  const [searchQuery, setSearchQuery] = useState("");

  // Determine which player list to use based on category selection
  const playerList = selectedCategoryId ? categoryPlayers : players;
  const isLoading = selectedCategoryId ? loadingCategoryPlayers : loading;

  // Filtrar jogadores com base na busca e no status (ativo/inativo)
  const filteredPlayers = playerList.filter(player => {
    // Primeiro, aplicar o filtro de busca
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const matchesSearch =
        player.name.toLowerCase().includes(query) ||
        player.position.toLowerCase().includes(query) ||
        (player.nationality && player.nationality.toLowerCase().includes(query));

      if (!matchesSearch) return false;
    }

    // Depois, aplicar o filtro de status baseado na tab ativa
    if (activeTab === "inativos") {
      return player.status === "inativo";
    } else if (activeTab === "lista" || activeTab === "cards" || activeTab === "tatica") {
      // Para as tabs de jogadores ativos (lista, cards, tatica), excluir inativos
      return player.status !== "inativo";
    }

    // Para outras tabs (membros), mostrar todos os jogadores
    return true;
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gestão de Elenco</h1>
          <p className="text-muted-foreground">
            Gerencie os jogadores do seu time, contratos e estatísticas.
          </p>
        </div>

        <div className="flex gap-2">
          {(role === "admin" || role === "president") && (
            <>
              <Button
                variant="outline"
                onClick={handleInitializePlayerStats}
                title="Inicializar estatísticas para jogadores sem dados"
              >
                Inicializar Stats
              </Button>
              <Button
                variant="outline"
                onClick={handleSyncPlayerStats}
                title="Recalcular todas as estatísticas dos jogadores"
              >
                Sincronizar Stats
              </Button>
            </>
          )}
          <Button className="bg-team-blue hover:bg-blue-700" onClick={() => {
            setSelectedPlayer(null);
            setEditMode(false);
            setPlayerDialogOpen(true);
          }}>
            <Plus className="h-4 w-4 mr-2" />
            Novo Jogador
          </Button>
        </div>
      </div>

      <Tabs defaultValue="lista" onValueChange={setActiveTab}>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
          <TabsList>
            <TabsTrigger value="lista">Atletas</TabsTrigger>
            <TabsTrigger value="inativos">Inativos</TabsTrigger>
            <TabsTrigger value="cards">Cards</TabsTrigger>
            <TabsTrigger value="tatica">Tática</TabsTrigger>
            {canApproveEvaluations && (
              <TabsTrigger value="aprovacoes">Aprovações</TabsTrigger>
            )}
            <TabsTrigger value="membros">Membros do Clube</TabsTrigger>
          </TabsList>

          <div className="relative w-full md:w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar jogador..."
              className="pl-8 pr-4"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Button variant="ghost" size="icon" className="absolute right-0 top-0">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <TabsContent value="lista">
          {isLoading ? (
            <div className="text-center py-8">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-team-blue border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
              <p className="mt-2 text-muted-foreground">Carregando jogadores...</p>
            </div>
          ) : filteredPlayers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {selectedCategoryId
                ? "Nenhum jogador encontrado nesta categoria."
                : "Nenhum jogador encontrado."}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gray-50 border-b">
                    <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">Nº</th>
                    <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">Jogador</th>
                    <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">Posição</th>
                    <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">Idade</th>
                    <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">Nacionalidade</th>
                    <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">Status</th>
                    <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">Ações</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredPlayers.map((player) => {
                    // Formatar a data de fim de contrato para exibição
                    let contractEnd = "Não informado";
                    if (player.contract_end_date) {
                      const date = new Date(player.contract_end_date);
                      const month = date.toLocaleString('pt-BR', { month: 'short' });
                      const year = date.getFullYear();
                      contractEnd = `${month} ${year}`;
                    }

                    return (
                      <tr
                        key={player.id}
                        className="border-b hover:bg-gray-50 cursor-pointer"
                        onClick={() => handleViewProfile(player.id)}
                      >
                        <td className="py-3 px-4 text-sm">{player.number}</td>
                        <td className="py-3 px-4">
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={player.image || ""} />
                              <AvatarFallback className="bg-team-blue text-white">
                                {player.name.slice(0, 2).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <span className="font-medium">{player.name}</span>
                          </div>
                        </td>
                        <td className="py-3 px-4 text-sm">{player.position}</td>
                        <td className="py-3 px-4 text-sm">{player.age}</td>
                        <td className="py-3 px-4 text-sm">{player.nationality || "—"}</td>
                        <td className="py-3 px-4 text-sm">
                          <select
                            value={player.status || "disponivel"}
                            onChange={async (e) => {
                              const newStatus = e.target.value;
                              await updatePlayer(player.club_id, player.id, { status: newStatus });
                              fetchPlayers(player.club_id); // Atualiza a lista
                            }}
                            className={`border rounded px-2 py-1 text-xs relative z-50
                              ${player.status === "disponivel" ? "bg-green-50 text-green-800 border-green-200" :
                                player.status === "lesionado" ? "bg-rose-50 text-rose-800 border-rose-200" :
                                player.status === "suspenso" ? "bg-amber-50 text-amber-800 border-amber-200" :
                                player.status === "em recuperacao" ? "bg-orange-50 text-orange-800 border-orange-200" :
                                player.status === "inativo" ? "bg-gray-50 text-gray-800 border-gray-200" :
                                player.status === "emprestado" ? "bg-primary/10 text-primary border-primary/20" : ""}
                            `}
                            style={{ position: "relative", zIndex: 50 }}
                            onClick={e => e.stopPropagation()} // Evita propagação do clique
                          >
                            {statusOptions.map(opt => (
                              <option key={opt.value} value={opt.value}>{opt.label}</option>
                            ))}
                          </select>
                        </td>
                        <td className="py-3 px-4 text-sm">{contractEnd}</td>
                        <td className="py-3 px-4 text-sm">
                          <div className="flex items-center justify-end">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={(e) => {
                                  e.stopPropagation();
                                  handleViewProfile(player.id);
                                }}>Ver perfil</DropdownMenuItem>
                                <DropdownMenuItem onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditPlayer(player);
                                }}>Editar</DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={(e) => {
                                  e.stopPropagation();
                                  navigate(`/jogador/${player.id}/historico-medico`);
                                }}>Histórico médico</DropdownMenuItem>
                                <DropdownMenuItem onClick={(e) => {
                                  e.stopPropagation();
                                  navigate(`/jogador/${player.id}/estatisticas`);
                                }}>Estatísticas</DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-red-600" onClick={async (e) => {
                                  e.stopPropagation();
                                  if (confirm(`Tem certeza que deseja remover ${player.name} do elenco?\n\nEsta ação irá remover PERMANENTEMENTE:\n• O jogador e todo seu histórico\n• Registros médicos\n• Estatísticas de partidas\n• Dados de treinamentos\n• Informações de alojamento\n• Dados financeiros\n• Todas as outras informações relacionadas\n\nEsta ação NÃO PODE ser desfeita!`)) {
                                    try {
                                      await deletePlayer(player.club_id, player.id);
                                      toast({
                                        title: "Jogador removido",
                                        description: `${player.name} foi removido permanentemente do elenco.`,
                                        variant: "default",
                                      });
                                      fetchPlayers(player.club_id); // Atualiza a lista
                                    } catch (error) {
                                      console.error('Erro ao remover jogador:', error);
                                      toast({
                                        title: "Erro ao remover jogador",
                                        description: error instanceof Error ? error.message : "Erro desconhecido ao remover jogador.",
                                        variant: "destructive",
                                      });
                                    }
                                  }
                                }}>
                                  Remover do elenco
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </TabsContent>

        <TabsContent value="inativos">
          {isLoading ? (
            <div className="text-center py-8">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-team-blue border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
              <p className="mt-2 text-muted-foreground">Carregando jogadores inativos...</p>
            </div>
          ) : filteredPlayers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {selectedCategoryId
                ? "Nenhum jogador inativo encontrado nesta categoria."
                : "Nenhum jogador inativo encontrado."}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gray-50 border-b">
                    <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">Nº</th>
                    <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">Jogador</th>
                    <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">Posição</th>
                    <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">Idade</th>
                    <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">Nacionalidade</th>
                    <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">Status</th>
                    <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">Ações</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredPlayers.map((player) => {
                    // Formatar a data de fim de contrato para exibição
                    let contractEnd = "Não informado";
                    if (player.contract_end_date) {
                      const date = new Date(player.contract_end_date);
                      const month = date.toLocaleString('pt-BR', { month: 'short' });
                      const year = date.getFullYear();
                      contractEnd = `${month} ${year}`;
                    }

                    return (
                      <tr
                        key={player.id}
                        className="border-b hover:bg-gray-50 cursor-pointer"
                        onClick={() => handleViewProfile(player.id)}
                      >
                        <td className="py-3 px-4 text-sm">{player.number}</td>
                        <td className="py-3 px-4">
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={player.image || ""} />
                              <AvatarFallback className="bg-team-blue text-white">
                                {player.name.slice(0, 2).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <span className="font-medium">{player.name}</span>
                          </div>
                        </td>
                        <td className="py-3 px-4 text-sm">{player.position}</td>
                        <td className="py-3 px-4 text-sm">{player.age}</td>
                        <td className="py-3 px-4 text-sm">{player.nationality || "—"}</td>
                        <td className="py-3 px-4 text-sm">
                          <select
                            value={player.status || "inativo"}
                            onChange={async (e) => {
                              const newStatus = e.target.value;

                              // Se o status for alterado para 'inativo', mostrar confirmação
                              if (newStatus === 'inativo') {
                                const confirmed = window.confirm(
                                  `Tem certeza que deseja alterar o status de ${player.name} para "Inativo"?\n\n` +
                                  'Esta ação irá remover o jogador de:\n' +
                                  '• Todas as categorias\n' +
                                  '• Alojamentos ativos\n' +
                                  '• Salários ativos\n' +
                                  '• Treinamentos futuros\n' +
                                  '• Convocações futuras\n' +
                                  '• Agenda de eventos\n' +
                                  '• Registros médicos pendentes\n\n' +
                                  'O histórico será preservado para consulta.'
                                );

                                if (!confirmed) {
                                  // Reverter a seleção se o usuário cancelar
                                  e.target.value = player.status || "inativo";
                                  return;
                                }
                              }

                              try {
                                await updatePlayer(player.club_id, player.id, { status: newStatus });

                                if (newStatus === 'inativo') {
                                  alert(`${player.name} foi alterado para "Inativo" e removido de todas as vinculações ativas.`);
                                }

                                fetchPlayers(player.club_id); // Atualiza a lista
                              } catch (error) {
                                console.error('Erro ao atualizar status:', error);
                                alert('Erro ao atualizar o status do jogador. Tente novamente.');
                                // Reverter a seleção em caso de erro
                                e.target.value = player.status || "inativo";
                              }
                            }}
                            className={`border rounded px-2 py-1 text-xs relative z-50
                              ${player.status === "disponivel" ? "bg-green-50 text-green-800 border-green-200" :
                                player.status === "lesionado" ? "bg-rose-50 text-rose-800 border-rose-200" :
                                player.status === "suspenso" ? "bg-amber-50 text-amber-800 border-amber-200" :
                                player.status === "em recuperacao" ? "bg-orange-50 text-orange-800 border-orange-200" :
                                player.status === "inativo" ? "bg-gray-50 text-gray-800 border-gray-200" :
                                player.status === "emprestado" ? "bg-primary/10 text-primary border-primary/20" : ""}
                            `}
                            style={{ position: "relative", zIndex: 50 }}
                            onClick={e => e.stopPropagation()} // Evita propagação do clique
                          >
                            {statusOptions.map(opt => (
                              <option key={opt.value} value={opt.value}>{opt.label}</option>
                            ))}
                          </select>
                        </td>
                        <td className="py-3 px-4 text-sm">{contractEnd}</td>
                        <td className="py-3 px-4 text-sm">
                          <div className="flex items-center justify-end">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={(e) => {
                                  e.stopPropagation();
                                  handleViewProfile(player.id);
                                }}>Ver perfil</DropdownMenuItem>
                                <DropdownMenuItem onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditPlayer(player);
                                }}>Editar</DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={(e) => {
                                  e.stopPropagation();
                                  navigate(`/jogador/${player.id}/historico-medico`);
                                }}>Histórico médico</DropdownMenuItem>
                                <DropdownMenuItem onClick={(e) => {
                                  e.stopPropagation();
                                  navigate(`/jogador/${player.id}/estatisticas`);
                                }}>Estatísticas</DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-red-600" onClick={async (e) => {
                                  e.stopPropagation();
                                  if (confirm(`Tem certeza que deseja remover ${player.name} do elenco?\n\nEsta ação irá remover PERMANENTEMENTE:\n• O jogador e todo seu histórico\n• Registros médicos\n• Estatísticas de partidas\n• Dados de treinamentos\n• Informações de alojamento\n• Dados financeiros\n• Todas as outras informações relacionadas\n\nEsta ação NÃO PODE ser desfeita!`)) {
                                    try {
                                      await deletePlayer(player.club_id, player.id);
                                      toast({
                                        title: "Jogador removido",
                                        description: `${player.name} foi removido permanentemente do elenco.`,
                                        variant: "default",
                                      });
                                      fetchPlayers(player.club_id); // Atualiza a lista
                                    } catch (error) {
                                      console.error('Erro ao remover jogador:', error);
                                      toast({
                                        title: "Erro ao remover jogador",
                                        description: error instanceof Error ? error.message : "Erro desconhecido ao remover jogador.",
                                        variant: "destructive",
                                      });
                                    }
                                  }
                                }}>
                                  Remover do elenco
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </TabsContent>

        <TabsContent value="cards">
          {isLoading ? (
            <div className="text-center py-8">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-team-blue border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
              <p className="mt-2 text-muted-foreground">Carregando jogadores...</p>
            </div>
          ) : filteredPlayers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {selectedCategoryId
                ? "Nenhum jogador encontrado nesta categoria."
                : activeTab === "inativos"
                  ? "Nenhum jogador inativo encontrado."
                  : "Nenhum jogador encontrado."}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {filteredPlayers.map((player) => (
                <PlayerCard
                  key={player.id}
                  player={player}
                  onClick={() => handleViewProfile(player.id)}
                  onEdit={() => handleEditPlayer(player)}
                  onStatusChange={async (newStatus) => {
                    try {
                      await updatePlayer(player.club_id, player.id, { status: newStatus });

                      if (newStatus === 'inativo') {
                        alert(`${player.name} foi alterado para "Inativo" e removido de todas as vinculações ativas.`);
                      }

                      fetchPlayers(player.club_id); // Atualiza a lista
                    } catch (error) {
                      console.error('Erro ao atualizar status:', error);
                      alert('Erro ao atualizar o status do jogador. Tente novamente.');
                    }
                  }}
                  onDelete={async (player) => {
                    if (confirm(`Tem certeza que deseja remover ${player.name} do elenco?\n\nEsta ação irá remover PERMANENTEMENTE:\n• O jogador e todo seu histórico\n• Registros médicos\n• Estatísticas de partidas\n• Dados de treinamentos\n• Informações de alojamento\n• Dados financeiros\n• Todas as outras informações relacionadas\n\nEsta ação NÃO PODE ser desfeita!`)) {
                      try {
                        await deletePlayer(player.club_id, player.id);
                        toast({
                          title: "Jogador removido",
                          description: `${player.name} foi removido permanentemente do elenco.`,
                          variant: "default",
                        });
                        fetchPlayers(player.club_id); // Atualiza a lista
                      } catch (error) {
                        console.error('Erro ao remover jogador:', error);
                        toast({
                          title: "Erro ao remover jogador",
                          description: error instanceof Error ? error.message : "Erro desconhecido ao remover jogador.",
                          variant: "destructive",
                        });
                      }
                    }
                  }}
                />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="tatica">
          <Card>
            <CardHeader className="pb-0">
              <CardTitle>Visão Tática</CardTitle>
              <CardDescription>
                Visualize seu elenco por posição e formação
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-8 text-center text-muted-foreground">
                Visualização tática em desenvolvimento...
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {canApproveEvaluations && (
          <TabsContent value="aprovacoes" className="space-y-6">
            <div className="space-y-6">
              {/* Estatísticas no topo */}
              <EvaluationApprovalStats />

              {/* Tabela de avaliações pendentes */}
              <Card>
                <CardHeader>
                  <CardTitle>Avaliações Pendentes de Aprovação</CardTitle>
                  <CardDescription>
                    Gerencie as avaliações que aguardam aprovação de gerentes e presidentes
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <PendingEvaluationsTable />
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        )}

        <TabsContent value="membros">
          <Card>
            <CardHeader className="pb-0">
              <CardTitle>Membros do Clube</CardTitle>
              <CardDescription>
                Total de {members.length} membros
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                data={members}
                columns={memberColumns}
                actions={memberActions}
                pageSize={5}
                isLoading={loadingMembers}
              />
              {errorMembers && <div className="text-red-500 mt-2">{errorMembers}</div>}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <PlayerDialog
        open={playerDialogOpen}
        onOpenChange={setPlayerDialogOpen}
        editMode={editMode}
        player={selectedPlayer}
        clubId={clubId}
      />
    </div>
  );
}
