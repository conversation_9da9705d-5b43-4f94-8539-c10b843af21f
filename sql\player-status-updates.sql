-- Add loan-related fields to players table
ALTER TABLE players
ADD COLUMN loan_end_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN loan_club_name TEXT;

-- Add rejection_reason to player_documents if it doesn't exist
ALTER TABLE player_documents
ADD COLUMN IF NOT EXISTS rejection_reason TEXT;

-- Function to remove all player associations when status becomes 'inativo'
CREATE OR REPLACE FUNCTION remove_player_associations(p_club_id INTEGER, p_player_id UUID)
RETURNS VOID AS $$
BEGIN
  -- Log the operation
  RAISE NOTICE 'Removendo vinculações do jogador % do clube %', p_player_id, p_club_id;

  -- 1. Remove from categories
  DELETE FROM player_categories
  WHERE club_id = p_club_id AND player_id = p_player_id;

  -- 2. Remove from accommodations (set status to 'completed' instead of deleting for history)
  UPDATE player_accommodations
  SET status = 'completed', check_out_date = CURRENT_DATE
  WHERE club_id = p_club_id AND player_id = p_player_id AND status = 'active';

  -- 3. Deactivate salaries (set status to 'inactive' instead of deleting for history)
  UPDATE player_salaries
  SET status = 'inactive', end_date = CURRENT_DATE
  WHERE club_id = p_club_id AND player_id = p_player_id AND status = 'active';

  -- 4. Remove from training participants
  DELETE FROM training_players
  WHERE club_id = p_club_id AND player_id = p_player_id;

  -- 5. Remove from callups (only future callups)
  DELETE FROM callup_players
  WHERE club_id = p_club_id AND player_id = p_player_id
  AND callup_id IN (
    SELECT id FROM callups
    WHERE club_id = p_club_id AND match_date > NOW()
  );

  -- 6. Deactivate player accounts
  UPDATE player_accounts
  SET expires_at = NOW()
  WHERE club_id = p_club_id AND player_id = p_player_id;

  -- 7. Remove from agenda events participants
  UPDATE agenda_events
  SET participants = array_remove(participants, p_player_id::text)
  WHERE club_id = p_club_id AND participants @> ARRAY[p_player_id::text];

  -- 8. Mark medical records as inactive (keep for history)
  UPDATE medical_records
  SET status = 'inactive'
  WHERE club_id = p_club_id AND player_id = p_player_id::text AND status != 'completed';

  -- 9. Cancel pending salary advances
  UPDATE salary_advances
  SET status = 'cancelled'
  WHERE club_id = p_club_id AND person_id::text = p_player_id::text
  AND person_type = 'player' AND status = 'active';

  -- 10. Mark evaluation invitations as inactive
  UPDATE player_evaluation_invitations
  SET status = 'inactive'
  WHERE club_id = p_club_id AND player_id = p_player_id AND status = 'pending';

  RAISE NOTICE 'Vinculações removidas com sucesso para o jogador %', p_player_id;
END;
$$ LANGUAGE plpgsql;

-- Function to handle player status changes
CREATE OR REPLACE FUNCTION handle_player_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if status changed to 'inativo'
  IF NEW.status = 'inativo' AND (OLD.status IS NULL OR OLD.status != 'inativo') THEN
    -- Remove all associations
    PERFORM remove_player_associations(NEW.club_id, NEW.id);

    -- Log the status change
    RAISE NOTICE 'Jogador % alterado para status inativo. Vinculações removidas.', NEW.id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically handle status changes
DROP TRIGGER IF EXISTS trigger_player_status_change ON players;

CREATE TRIGGER trigger_player_status_change
  AFTER UPDATE OF status ON players
  FOR EACH ROW
  EXECUTE FUNCTION handle_player_status_change();
